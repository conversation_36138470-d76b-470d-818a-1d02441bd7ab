import { orpc, type QueryOptions } from "@/lib/orpc";
import type { UserResponseData } from "@/types/user";
import { useQuery } from "@tanstack/react-query";
import { fetchGameConfig } from "../../app/fetchGameConfig";
import { useAuthStore, usePersistStore } from "../../app/store/stores";

const useFetchCurrentUser = (options: QueryOptions = {}) => {
    const { staleCurrentUserData, setStaleCurrentUserData, gameConfig } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);

    return useQuery<UserResponseData>(
        orpc.user.getCurrentUserInfo.queryOptions({
            enabled: !!authed,
            staleTime: 30000,
            placeholderData: staleCurrentUserData,
            ...options,
            select: (data: UserResponseData) => {
                // Update stale user data if it's different
                if (data !== staleCurrentUserData) {
                    setStaleCurrentUserData(data);
                }

                // Update game config if it doesn't exist or if the version is different
                if (!gameConfig || gameConfig.version !== data.gameConfigVersion) {
                    console.log("Fetching game config", data.gameConfigVersion);
                    // Non blocking
                    fetchGameConfig(data.gameConfigVersion);
                }

                return data;
            },
        })
    );
};

export default useFetchCurrentUser;
