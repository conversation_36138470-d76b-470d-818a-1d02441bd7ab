import { orpc, type QueryOptions } from "@/lib/orpc";
import type { UserResponseData } from "@/types/user";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { fetchGameConfig } from "../../app/fetchGameConfig";
import { useAuthStore, usePersistStore } from "../../app/store/stores";

// Fetch current user info and update game config if there are any changes
// Persists stale user data in localstorage to use as placeholderData on initial load
const useFetchCurrentUser = (options: QueryOptions = {}) => {
    const { staleCurrentUserData, setStaleCurrentUserData, gameConfig } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);
    const lastGameConfigVersionRef = useRef<number | null>(null);

    const query = useQuery<UserResponseData>(
        orpc.user.getCurrentUserInfo.queryOptions({
            enabled: !!authed,
            staleTime: 30000,
            placeholderData: staleCurrentUserData,
            ...options,
        })
    );

    // Handle side effects in useEffect instead of select function
    useEffect(() => {
        if (query.data) {
            // Update stale user data if it's different (deep comparison for objects)
            const currentDataString = JSON.stringify(query.data);
            const staleDataString = JSON.stringify(staleCurrentUserData);

            if (currentDataString !== staleDataString) {
                setStaleCurrentUserData(query.data);
            }

            // Update game config if it doesn't exist or if the version is different
            const currentGameConfigVersion = query.data.gameConfigVersion;
            if (
                currentGameConfigVersion &&
                (!gameConfig ||
                    gameConfig.version !== currentGameConfigVersion ||
                    lastGameConfigVersionRef.current !== currentGameConfigVersion)
            ) {
                lastGameConfigVersionRef.current = currentGameConfigVersion;
                // Non blocking
                fetchGameConfig(currentGameConfigVersion);
            }
        }
    }, [query.data, staleCurrentUserData, gameConfig, setStaleCurrentUserData]);

    return query;
};

export default useFetchCurrentUser;
